import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import '../../../../models/api_profile_model.dart';
import '../../../../models/rfid_order_model.dart';
import '../../../../core/api/api_service.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/api/api_config.dart';
import '../../../../services/payment/phonepe_service.dart';
import '../../../../services/payment/payu_service.dart';
import '../../../../services/payment/cashfree_service.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';

class RfidOrderForm extends StatefulWidget {
  final ProfileData? profileData;

  const RfidOrderForm({super.key, this.profileData});

  @override
  State<RfidOrderForm> createState() => _RfidOrderFormState();
}

class _RfidOrderFormState extends State<RfidOrderForm> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _pincodeController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _gstController = TextEditingController();
  final TextEditingController _businessNameController = TextEditingController();

  bool _isLoading = false;

  // Payment flow state management
  bool _isPaymentInProgress = false;
  double? _pendingPaymentAmount;
  String? _currentTransactionId;
  String _selectedPaymentMethod = 'PhonePe'; // Default payment method

  // API service instance
  final ApiService _apiService = ApiService();

  // Indian states list for dropdown
  final List<String> _indianStates = [
    'Andhra Pradesh',
    'Arunachal Pradesh',
    'Assam',
    'Bihar',
    'Chhattisgarh',
    'Goa',
    'Gujarat',
    'Haryana',
    'Himachal Pradesh',
    'Jharkhand',
    'Karnataka',
    'Kerala',
    'Madhya Pradesh',
    'Maharashtra',
    'Manipur',
    'Meghalaya',
    'Mizoram',
    'Nagaland',
    'Odisha',
    'Punjab',
    'Rajasthan',
    'Sikkim',
    'Tamil Nadu',
    'Telangana',
    'Tripura',
    'Uttar Pradesh',
    'Uttarakhand',
    'West Bengal',
    'Andaman and Nicobar Islands',
    'Chandigarh',
    'Dadra and Nagar Haveli and Daman and Diu',
    'Delhi',
    'Jammu and Kashmir',
    'Ladakh',
    'Lakshadweep',
    'Puducherry',
  ];

  @override
  void initState() {
    super.initState();

    debugPrint('🔔 RFID: ========== RFID ORDER FORM INIT START ==========');
    debugPrint('🔔 RFID: Widget mounted: $mounted');
    debugPrint('🔔 RFID: Profile data provided: ${widget.profileData != null}');

    try {
      // Pre-populate form fields with profile data
      _populateFormFields();
      debugPrint('✅ RFID: Form fields populated successfully');
    } catch (e) {
      debugPrint('❌ RFID: Error populating form fields: $e');
    }

    // Verify API configuration in debug mode
    if (kDebugMode) {
      try {
        _apiService.verifyRfidApiConfiguration();
        debugPrint('✅ RFID: API configuration verified');
      } catch (e) {
        debugPrint('❌ RFID: Error verifying API configuration: $e');
      }
    }

    debugPrint('🔔 RFID: ========== RFID ORDER FORM INIT END ==========');
  }

  void _populateFormFields() {
    if (widget.profileData != null) {
      final profile = widget.profileData!;

      // Pre-populate form fields with API data
      _nameController.text = profile.name;
      _emailController.text = profile.email;
      _phoneController.text = profile.mobileNumber;
      _addressController.text = profile.address;
      _stateController.text = profile.state;
      _pincodeController.text = profile.pincode;

      // Pre-populate GST and business name if available
      if (profile.gstNo != null && profile.gstNo!.isNotEmpty) {
        _gstController.text = profile.gstNo!;
      }
      if (profile.businessName != null && profile.businessName!.isNotEmpty) {
        _businessNameController.text = profile.businessName!;
      }

      // Set default amount if needed
      _amountController.text = '500'; // Default RFID card cost
    }
  }

  /// Validate email format
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter an email address';
    }
    final emailRegex = RegExp(r'^\w+([\.-]?\w+)*@\w+([\.-]?\w+)+$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  /// Validate name
  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your full name';
    }
    if (value.length < 3) {
      return 'Name must be at least 3 characters long';
    }
    return null;
  }

  /// Validate address
  String? _validateAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your address';
    }
    if (value.length < 10) {
      return 'Address must be at least 10 characters long';
    }
    return null;
  }

  /// Validate state
  String? _validateState(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a state';
    }
    return null;
  }

  /// Validate pincode
  String? _validatePincode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter pincode';
    }
    final pincodeRegex = RegExp(r'^\d{6}$');
    if (!pincodeRegex.hasMatch(value)) {
      return 'Pincode must be a 6-digit number';
    }
    return null;
  }

  /// Validate amount
  String? _validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter amount';
    }
    final amount = double.tryParse(value);
    if (amount == null || amount < 500) {
      return 'Please enter a valid amount! Amount should be at least ₹500';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🔔 RFID: ========== BUILD METHOD START ==========');
    debugPrint('🔔 RFID: Widget mounted: $mounted');
    debugPrint('🔔 RFID: Loading state: $_isLoading');
    debugPrint('🔔 RFID: Payment in progress: $_isPaymentInProgress');

    // Safety check for widget state
    if (!mounted) {
      debugPrint('❌ RFID: Widget not mounted, returning empty container');
      return Container();
    }

    try {
      return Scaffold(
        backgroundColor: const Color(0xFF0E0E0E), // Dark background

        appBar: AppBar(
          backgroundColor: const Color(0xFF0E0E0E),
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back,
                color: Color(0xFF67C44C)), // Green arrow
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text(
            'RFID Order Form',
            style: TextStyle(color: Colors.white),
          ),
        ),

        // Use a Column so we can pin the "Submit Order" button at the bottom.
        body: SafeArea(
          child: Column(
            children: [
              // 1) Scrollable Form content
              Expanded(
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Contact Info Section
                        const Text(
                          'Contact Information',
                          style: TextStyle(
                            color: Color(0xFF67C44C),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Full Name
                        _buildTextField(
                          label: 'Full Name *',
                          controller: _nameController,
                          keyboardType: TextInputType.text,
                          validator: _validateName,
                        ),
                        const SizedBox(height: 8),
                        // Email
                        _buildTextField(
                          label: 'Email Address *',
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          validator: _validateEmail,
                        ),
                        const SizedBox(height: 8),
                        // Phone
                        _buildTextField(
                          label: 'Phone Number *',
                          controller: _phoneController,
                          keyboardType: TextInputType.phone,
                          validator: (value) => (value == null || value.isEmpty)
                              ? 'Please enter your phone number'
                              : null,
                        ),
                        const SizedBox(height: 16),

                        // Address Info Section
                        const Text(
                          'Address Information',
                          style: TextStyle(
                            color: Color(0xFF67C44C),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Street Address
                        _buildTextField(
                          label: 'Street Address *',
                          controller: _addressController,
                          keyboardType: TextInputType.streetAddress,
                          validator: _validateAddress,
                        ),
                        const SizedBox(height: 8),

                        // State & Pincode in one row with proper constraints
                        IntrinsicHeight(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // State
                              Expanded(
                                flex: 1,
                                child: _buildStateDropdown(),
                              ),
                              const SizedBox(width: 12),
                              // Pincode
                              Expanded(
                                flex: 1,
                                child: _buildTextField(
                                  label: 'Pincode *',
                                  controller: _pincodeController,
                                  keyboardType: TextInputType.number,
                                  validator: _validatePincode,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Payment Details Section
                        const Text(
                          'Payment Details',
                          style: TextStyle(
                            color: Color(0xFF67C44C),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Amount
                        _buildTextField(
                          label: 'Amount *',
                          controller: _amountController,
                          keyboardType: TextInputType.number,
                          validator: _validateAmount,
                        ),
                        const SizedBox(height: 16),

                        // Payment Method Selection
                        const Text(
                          'Payment Method *',
                          style: TextStyle(
                            color: Color(0xFF67C44C),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: DropdownButtonFormField<String>(
                            value: _selectedPaymentMethod,
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                            ),
                            items: const [
                              DropdownMenuItem(
                                value: 'PhonePe',
                                child: Row(
                                  children: [
                                    Icon(Icons.phone_android,
                                        color: Color(0xFF5F259F), size: 20),
                                    SizedBox(width: 8),
                                    Text('PhonePe'),
                                  ],
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'PayU',
                                child: Row(
                                  children: [
                                    Icon(Icons.payment,
                                        color: Color(0xFF00A651), size: 20),
                                    SizedBox(width: 8),
                                    Text('PayU'),
                                  ],
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'Cashfree',
                                child: Row(
                                  children: [
                                    Icon(Icons.account_balance_wallet,
                                        color: Color(0xFF0066CC), size: 20),
                                    SizedBox(width: 8),
                                    Text('Cashfree'),
                                  ],
                                ),
                              ),
                            ],
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _selectedPaymentMethod = newValue;
                                });
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select a payment method';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Business Details Section (Optional)
                        if (widget.profileData?.gstNo != null ||
                            widget.profileData?.businessName != null) ...[
                          const Text(
                            'Business Details (Optional)',
                            style: TextStyle(
                              color: Color(0xFF67C44C),
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Business Name
                          if (widget.profileData?.businessName != null)
                            _buildTextField(
                              label: 'Business Name',
                              controller: _businessNameController,
                              keyboardType: TextInputType.text,
                            ),
                          const SizedBox(height: 8),

                          // GST Number
                          if (widget.profileData?.gstNo != null)
                            _buildTextField(
                              label: 'GST Number',
                              controller: _gstController,
                              keyboardType: TextInputType.text,
                            ),
                          const SizedBox(height: 16),
                        ],
                      ],
                    ),
                  ),
                ),
              ),

              // 2) Pinned Submit Button at the bottom
              Container(
                color: const Color(0xFF0E0E0E),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: SizedBox(
                  width: double.infinity,
                  height: 44, // Slightly smaller height
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF67C44C), // Green accent
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: (_isLoading || _isPaymentInProgress)
                        ? null
                        : _handleSubmit,
                    child: (_isLoading || _isPaymentInProgress)
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  color: Colors.black,
                                  strokeWidth: 2,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _isPaymentInProgress
                                    ? 'Processing Payment...'
                                    : 'Submitting...',
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : const Text(
                            'Submit Order & Pay',
                            style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('❌ RFID: Error in build method: $e');
      debugPrint('❌ RFID: Stack trace: $stackTrace');

      // Return error screen
      return Scaffold(
        backgroundColor: const Color(0xFF0E0E0E),
        appBar: AppBar(
          backgroundColor: const Color(0xFF0E0E0E),
          title: const Text(
            'RFID Order Form',
            style: TextStyle(color: Colors.white),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Error Loading RFID Order Form',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Error: $e',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      // Trigger rebuild
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF67C44C),
                  ),
                  child: const Text(
                    'Retry',
                    style: TextStyle(color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required TextInputType keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 13, // Slightly smaller label text
          ),
        ),
        const SizedBox(height: 4),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          style: const TextStyle(color: Colors.white, fontSize: 14),
          validator: validator,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            filled: true,
            fillColor: const Color(0xFF1E1E1E),
            hintText: _hintForLabel(label),
            hintStyle: const TextStyle(color: Colors.white38, fontSize: 13),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.transparent),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  // Provide placeholder text based on label if desired.
  String _hintForLabel(String label) {
    if (label.toLowerCase().contains('full name')) {
      return 'Enter your full name';
    } else if (label.toLowerCase().contains('email')) {
      return 'Enter your email address';
    } else if (label.toLowerCase().contains('phone')) {
      return 'Enter your phone number';
    } else if (label.toLowerCase().contains('street address')) {
      return 'Enter your street address';
    } else if (label.toLowerCase().contains('state')) {
      return 'Enter your state';
    } else if (label.toLowerCase().contains('pincode')) {
      return 'Enter 6-digit pincode';
    } else if (label.toLowerCase().contains('amount')) {
      return 'Enter amount';
    } else if (label.toLowerCase().contains('business name')) {
      return 'Enter your business name';
    } else if (label.toLowerCase().contains('gst')) {
      return 'Enter your GST number';
    }
    return '';
  }

  /// Handle form submission with PhonePe payment integration
  /// Following the React code flow: validate → order creation → PhonePe payment
  Future<void> _handleSubmit() async {
    debugPrint('🔔 RFID: ========== RFID ORDER SUBMISSION START ==========');

    // Validate form fields (matching React validation logic)
    if (!(_formKey.currentState?.validate() ?? false)) {
      debugPrint('❌ RFID: Form validation failed');
      return;
    }

    // Additional amount validation (matching React code)
    final amount = double.tryParse(_amountController.text.trim());
    if (amount == null || amount < 500) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Please enter a valid amount! Amount should be at least ₹500'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    setState(() {
      _isLoading = true;
      _isPaymentInProgress = true;
      _pendingPaymentAmount = amount;
    });

    try {
      debugPrint('🔔 RFID: Creating order data...');

      // Create order data (matching React formData structure)
      final orderData = RfidOrderData(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        mobileNumber: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        state: _stateController.text.trim(),
        pincode: _pincodeController.text.trim(),
        amount: _amountController.text.trim(),
      );

      debugPrint('🔔 RFID: Calling order initialization API...');

      // Initialize order with API (matching React: axios.post('/orders/init-v2', formData))
      final response =
          await _apiService.initializeRfidOrder(orderData.toJson());

      debugPrint(
          '🔔 RFID: Order API response received: ${response['success']}');

      if (response['success'] == true) {
        debugPrint(
            '✅ RFID: Order created successfully, initiating $_selectedPaymentMethod payment...');

        // Extract order details for payment (matching React: let order = res.data)
        final orderResponse = response;

        // Route to appropriate payment method
        if (_selectedPaymentMethod == 'PhonePe') {
          // Initiate PhonePe payment (matching React: initPhonePeSDK(order))
          await _initPhonePeSDK(orderResponse, amount);
        } else if (_selectedPaymentMethod == 'PayU') {
          // Initiate PayU payment
          await _initPayUSDK(orderResponse, amount);
        } else if (_selectedPaymentMethod == 'Cashfree') {
          // Initiate Cashfree payment
          await _initCashfreeSDK(orderResponse, amount);
        } else {
          throw Exception(
              'Unsupported payment method: $_selectedPaymentMethod');
        }
      } else {
        // Handle order creation failure
        setState(() {
          _isLoading = false;
          _isPaymentInProgress = false;
          _pendingPaymentAmount = null;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Failed to place order'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ RFID: Exception during order creation: $e');

      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
      });

      String errorMessage = 'Failed to place order';
      if (e is ApiException) {
        errorMessage = e.message;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Initialize PhonePe SDK and start payment transaction
  /// Matching React code: initPhonePeSDK(order)
  Future<void> _initPhonePeSDK(
      Map<String, dynamic> order, double amount) async {
    debugPrint(
        '🔔 RFID: ========== PHONEPE SDK INITIALIZATION START ==========');
    debugPrint('🔔 RFID: Payment amount: ₹$amount');

    try {
      // Show payment initiation loading
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Initializing PhonePe payment...'),
              ],
            ),
            duration: Duration(seconds: 30),
          ),
        );
      }

      // Extract values from order response with null safety
      final environment = order['environment']?.toString();
      final merchantId =
          order['merchantId']?.toString() ?? order['merchant_id']?.toString();
      final txnId = order['txnId']?.toString() ??
          order['transaction_id']?.toString() ??
          order['txn_id']?.toString();

      // Validate required parameters
      if (environment == null || environment.isEmpty) {
        throw Exception(
            'PhonePe environment is missing from server response. Available keys: ${order.keys.toList()}');
      }
      if (merchantId == null || merchantId.isEmpty) {
        throw Exception(
            'PhonePe merchantId is missing from server response. Available keys: ${order.keys.toList()}');
      }
      if (txnId == null || txnId.isEmpty) {
        throw Exception(
            'PhonePe txnId is missing from server response. Available keys: ${order.keys.toList()}');
      }

      // Store transaction ID for response handling
      _currentTransactionId = txnId;

      debugPrint('🔔 RFID: Environment: $environment');
      debugPrint('🔔 RFID: MerchantId: $merchantId');
      debugPrint('🔔 RFID: TxnId: $txnId');

      // Initialize PhonePe SDK (matching React: phonepeSDK.init())
      final flowId = 'rfid_${txnId}_${DateTime.now().millisecondsSinceEpoch}';
      final inited = await PhonePeService.init(
        environment: environment,
        merchantId: merchantId,
        flowId: flowId,
        enableLogging: true,
      );

      if (!inited) {
        throw Exception('PhonePe SDK initialization failed');
      }

      debugPrint('✅ RFID: PhonePe SDK initialized successfully');

      // Prepare payment request (matching React: JSON.stringify(order.payload))
      final paymentRequest = {
        "orderId": order['payload']['orderId'],
        "merchantId": order['payload']['merchantId'],
        "token": order['payload']['token'],
        "paymentMode": {"type": "PAY_PAGE"}
      };

      final requestBody = jsonEncode(paymentRequest);
      const appSchema = 'com.eeil.ecoplug';

      debugPrint('🔔 RFID: Starting PhonePe transaction...');

      // Update loading message
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Opening PhonePe...'),
              ],
            ),
            duration: Duration(minutes: 5),
          ),
        );
      }

      // Start PhonePe transaction (matching React: phonepeSDK.startTransaction())
      final result = await PhonePeService.startPGTransaction(
        request: requestBody,
        appSchema: appSchema,
        timeout: const Duration(minutes: 5),
      );

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      debugPrint(
          '🔔 RFID: PhonePe transaction completed with result: ${result.type}');

      // Handle payment result (matching React: PhonepeResponse(res, order.txnId))
      await _handlePhonepeResponse(result, txnId);
    } catch (e) {
      debugPrint('❌ RFID: PhonePe payment error: $e');

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment initialization failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Handle PhonePe payment response
  /// Matching React code: PhonepeResponse(res, txnId, checksum)
  Future<void> _handlePhonepeResponse(
      PaymentResult result, String txnId) async {
    debugPrint(
        '🔔 RFID: ========== PHONEPE RESPONSE HANDLING START ==========');
    debugPrint('🔔 RFID: Result type: ${result.type}');
    debugPrint('🔔 RFID: Transaction ID: $txnId');

    try {
      final payload = {
        'code': (result.data ?? {})['statusCode']?.toString() ??
            (result.data ?? {})['status']?.toString() ??
            result.type.toString().split('.').last.toUpperCase(),
        'transactionId': txnId,
        'response': result.data ?? {},
        'call_back_response': result.data ?? {},
      };

      debugPrint('🔔 RFID: Sending payment response to server...');

      // Send response to server (matching React: axios.post('/payment/response-phonepev2'))
      final response = await _apiService.handlePhonePeResponse(payload);

      debugPrint('🔔 RFID: Server response received: ${response['success']}');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        if (response['success'] == true) {
          // Payment successful (matching React: snackBarSuccess(data.message))
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(response['message'] ??
                        'RFID order payment successful!'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );

          // Show success dialog and navigate back (matching React: props.navigation.goBack())
          _showPaymentSuccessDialog(response);
        } else {
          // Payment failed (matching React: snackBarError(data.message))
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Payment failed'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ RFID: Error handling payment response: $e');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment processing failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Initialize PayU SDK and start payment transaction
  /// Following the PhonePe pattern but adapted for PayU
  Future<void> _initPayUSDK(Map<String, dynamic> order, double amount) async {
    debugPrint('🔔 RFID: ========== PAYU SDK INITIALIZATION START ==========');
    debugPrint('🔔 RFID: Payment amount: ₹$amount');
    debugPrint('🔔 RFID: Widget mounted: $mounted');

    if (!mounted) {
      debugPrint('❌ RFID: Widget not mounted, aborting PayU payment');
      debugPrint(
          '🔔 RFID: ========== PAYU SDK INITIALIZATION END (NOT MOUNTED) ==========');
      return;
    }

    try {
      debugPrint('🔔 RFID: Extracting PayU parameters from order response...');

      // DETAILED SERVER RESPONSE ANALYSIS FOR PAYU
      debugPrint(
          '🔍 RFID-PAYU: ========== DETAILED SERVER RESPONSE ANALYSIS ==========');
      debugPrint('🔍 RFID-PAYU: Response keys count: ${order.keys.length}');

      debugPrint('🔍 RFID-PAYU: All available keys in response:');
      for (String key in order.keys) {
        final value = order[key];
        final valueType = value.runtimeType;
        final valuePreview = value.toString().length > 50
            ? '${value.toString().substring(0, 50)}...'
            : value.toString();
        debugPrint('🔍 RFID-PAYU:   [$key] ($valueType): $valuePreview');
      }

      // Check for PayU-specific fields
      debugPrint('🔍 RFID-PAYU: Checking for PayU field variations:');
      debugPrint(
          '🔍 RFID-PAYU:   merchantKey: ${order.containsKey('merchantKey')} (${order['merchantKey']?.runtimeType})');
      debugPrint(
          '🔍 RFID-PAYU:   merchant_key: ${order.containsKey('merchant_key')} (${order['merchant_key']?.runtimeType})');
      debugPrint(
          '🔍 RFID-PAYU:   key: ${order.containsKey('key')} (${order['key']?.runtimeType})');
      debugPrint(
          '🔍 RFID-PAYU:   environment: ${order.containsKey('environment')} (${order['environment']?.runtimeType})');
      debugPrint(
          '🔍 RFID-PAYU:   env: ${order.containsKey('env')} (${order['env']?.runtimeType})');
      debugPrint(
          '🔍 RFID-PAYU:   txnId: ${order.containsKey('txnId')} (${order['txnId']?.runtimeType})');
      debugPrint(
          '🔍 RFID-PAYU:   transaction_id: ${order.containsKey('transaction_id')} (${order['transaction_id']?.runtimeType})');
      debugPrint(
          '🔍 RFID-PAYU:   txn_id: ${order.containsKey('txn_id')} (${order['txn_id']?.runtimeType})');
      debugPrint('🔍 RFID-PAYU: ========== END DETAILED ANALYSIS ==========');

      // Check if the response indicates an error
      if (order.containsKey('error') || order.containsKey('message')) {
        final errorMessage =
            order['error'] ?? order['message'] ?? 'Unknown server error';
        debugPrint('❌ RFID: Server returned error: $errorMessage');
        throw Exception('PayU server error: $errorMessage');
      }

      // Extract values from order response with null safety
      // The server returns parameters in nested payUPaymentParams object (same as wallet)
      String? environment;
      String? merchantKey;
      String? txnId;

      // First try to extract from nested payUPaymentParams object (actual server format)
      if (order.containsKey('payUPaymentParams') &&
          order['payUPaymentParams'] is Map) {
        final payUParams = order['payUPaymentParams'] as Map<String, dynamic>;
        merchantKey = payUParams['key']?.toString();
        environment = payUParams['environment']?.toString();
        txnId = payUParams['transactionId']?.toString();
        debugPrint('🔔 RFID: Extracted from payUPaymentParams nested object');
      }

      // Fallback: try root level with different field name variations
      if (environment == null) {
        environment =
            order['environment']?.toString() ?? order['env']?.toString();
        debugPrint('🔔 RFID: Extracted environment from root level');
      }

      if (merchantKey == null) {
        merchantKey = order['merchantKey']?.toString() ??
            order['merchant_key']?.toString() ??
            order['key']?.toString();
        debugPrint('🔔 RFID: Extracted merchantKey from root level');
      }

      if (txnId == null) {
        txnId = order['txnId']?.toString() ??
            order['transaction_id']?.toString() ??
            order['txn_id']?.toString();
        debugPrint('🔔 RFID: Extracted txnId from root level');
      }

      debugPrint('🔔 RFID: Extracted parameters:');
      debugPrint(
          '🔔 RFID: merchantKey: ${merchantKey != null ? (merchantKey.length > 8 ? "${merchantKey.substring(0, 8)}..." : merchantKey) : "NULL"}');
      debugPrint('🔔 RFID: environment: $environment');
      debugPrint('🔔 RFID: txnId: $txnId');

      // Validate required parameters
      if (merchantKey == null || merchantKey.isEmpty) {
        debugPrint('❌ RFID: merchantKey validation failed');
        debugPrint(
            '❌ RFID: Available keys in response: ${order.keys.toList()}');
        throw Exception(
            'PayU merchantKey is missing from server response. Available keys: ${order.keys.toList()}');
      }
      if (environment == null || environment.isEmpty) {
        debugPrint('❌ RFID: environment validation failed');
        throw Exception(
            'PayU environment is missing from server response. Available keys: ${order.keys.toList()}');
      }
      if (txnId == null || txnId.isEmpty) {
        debugPrint('❌ RFID: txnId validation failed');
        throw Exception(
            'PayU txnId is missing from server response. Available keys: ${order.keys.toList()}');
      }

      // Store transaction ID for response handling
      _currentTransactionId = txnId;

      debugPrint('🔔 RFID: Environment: $environment');
      debugPrint(
          '🔔 RFID: MerchantKey: ${merchantKey.length > 8 ? "${merchantKey.substring(0, 8)}..." : merchantKey}');
      debugPrint('🔔 RFID: TxnId: $txnId');

      debugPrint('🔔 RFID: ========== PAYU SDK INITIALIZATION ==========');
      // Initialize PayU SDK
      final inited = await PayUService.init(
        merchantKey: merchantKey,
        environment: environment,
        enableLogging: true,
      );

      if (!inited) {
        throw Exception('PayU SDK initialization failed');
      }

      debugPrint('✅ RFID: PayU SDK initialized successfully');

      // Use the complete payUPaymentParams from server response (same as wallet)
      Map<String, dynamic> paymentParams;

      if (order.containsKey('payUPaymentParams') &&
          order['payUPaymentParams'] is Map) {
        // Use server-provided payment parameters
        paymentParams = Map<String, dynamic>.from(
            order['payUPaymentParams'] as Map<String, dynamic>);
        debugPrint('🔔 RFID: Using server-provided payUPaymentParams');
        debugPrint(
            '🔔 RFID: Server params keys: ${paymentParams.keys.toList()}');

        // Ensure required fields are properly formatted with correct PayU parameter names
        paymentParams['key'] = merchantKey;
        paymentParams['txnid'] = txnId;
        paymentParams['amount'] = amount.toString();
        paymentParams['environment'] = environment;

        // Fix parameter name mapping for PayU SDK requirements
        if (paymentParams.containsKey('productInfo')) {
          paymentParams['productinfo'] = paymentParams['productInfo'];
          paymentParams.remove('productInfo');
        }
        if (paymentParams.containsKey('firstName')) {
          paymentParams['firstname'] = paymentParams['firstName'];
          paymentParams.remove('firstName');
        }

        // Override with RFID-specific user data
        paymentParams['firstname'] = _nameController.text.trim();
        paymentParams['email'] = _emailController.text.trim();
        paymentParams['phone'] = _phoneController.text.trim();
        paymentParams['productinfo'] = 'EcoPlug RFID Card';

        // Add mobile app specific URLs if not present
        if (!paymentParams.containsKey('surl')) {
          paymentParams['surl'] = 'com.eeil.ecoplug://payu/success';
        }
        if (!paymentParams.containsKey('furl')) {
          paymentParams['furl'] = 'com.eeil.ecoplug://payu/failure';
        }

        debugPrint(
            '🔔 RFID: Using server-provided payment parameters with RFID-specific data');
      } else {
        // Fallback: Build PayU payment parameters manually
        debugPrint(
            '🔔 RFID: Server payUPaymentParams not found, building manually');
        paymentParams = {
          // Required PayU parameters
          'key': merchantKey,
          'txnid': txnId,
          'amount': amount.toString(),
          'productinfo': 'EcoPlug RFID Card',
          'firstname': _nameController.text.trim(),
          'email': _emailController.text.trim(),
          'phone': _phoneController.text.trim(),

          // Success and failure URLs
          'surl': 'com.eeil.ecoplug://payu/success',
          'furl': 'com.eeil.ecoplug://payu/failure',

          // Environment and additional parameters
          'environment': environment,
          'userCredential':
              '$merchantKey:rfid_${DateTime.now().millisecondsSinceEpoch}',

          // Optional parameters for better UX
          'udf1': 'rfid_card',
          'udf2': 'ecoplug_app',
          'udf3': '',
          'udf4': '',
          'udf5': '',
        };
      }

      debugPrint('🔔 RFID: Starting PayU transaction...');

      // Update loading message
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Opening PayU...'),
              ],
            ),
            duration: Duration(minutes: 5),
          ),
        );
      }

      // Start PayU transaction
      final result = await PayUService.startPayment(
        paymentParams: paymentParams,
        timeout: const Duration(minutes: 5),
      );

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      debugPrint(
          '🔔 RFID: PayU transaction completed with result: ${result.type}');

      // Handle payment result
      await _handlePayUResponse(result, txnId);
    } catch (e) {
      debugPrint('❌ RFID: PayU payment error: $e');

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('PayU payment initialization failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Handle PayU payment response
  /// Following the PhonePe pattern but adapted for PayU
  Future<void> _handlePayUResponse(
      PayUPaymentResult result, String txnId) async {
    debugPrint('🔔 RFID: ========== PAYU RESPONSE HANDLING START ==========');
    debugPrint('🔔 RFID: Result type: ${result.type}');
    debugPrint('🔔 RFID: Transaction ID: $txnId');

    try {
      final payload = {
        'code': result.data?['status']?.toString() ??
            result.type.toString().split('.').last.toUpperCase(),
        'transactionId': txnId,
        'response': result.data ?? {},
        'call_back_response': result.data ?? {},
      };

      debugPrint('🔔 RFID: Sending PayU payment response to server...');

      // Send response to server (similar to PhonePe but for PayU endpoint)
      final response = await _apiService.handlePayUResponse(payload);

      debugPrint('🔔 RFID: Server response received: ${response['success']}');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        if (response['success'] == true) {
          // Payment successful
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Payment successful!'),
              backgroundColor: Colors.green,
            ),
          );

          // Show success dialog and navigate back
          _showPaymentSuccessDialog(response);
        } else {
          // Payment failed
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Payment failed'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ RFID: Error handling PayU payment response: $e');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PayU payment processing failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Initialize Cashfree SDK and start payment transaction
  /// Following the PhonePe and PayU patterns but adapted for Cashfree
  Future<void> _initCashfreeSDK(
      Map<String, dynamic> order, double amount) async {
    debugPrint(
        '🔔 RFID: ========== CASHFREE SDK INITIALIZATION START ==========');
    debugPrint('🔔 RFID: Payment amount: ₹$amount');
    debugPrint('🔔 RFID: Widget mounted: $mounted');

    if (!mounted) {
      debugPrint('❌ RFID: Widget not mounted, aborting Cashfree payment');
      debugPrint(
          '🔔 RFID: ========== CASHFREE SDK INITIALIZATION END (NOT MOUNTED) ==========');
      return;
    }

    try {
      debugPrint(
          '🔔 RFID: Extracting Cashfree parameters from order response...');

      // DETAILED SERVER RESPONSE ANALYSIS FOR CASHFREE
      debugPrint(
          '🔍 RFID-CASHFREE: ========== DETAILED SERVER RESPONSE ANALYSIS ==========');
      debugPrint('🔍 RFID-CASHFREE: Response keys count: ${order.keys.length}');

      debugPrint('🔍 RFID-CASHFREE: All available keys in response:');
      for (String key in order.keys) {
        final value = order[key];
        final valueType = value.runtimeType;
        final valuePreview = value.toString().length > 50
            ? '${value.toString().substring(0, 50)}...'
            : value.toString();
        debugPrint('🔍 RFID-CASHFREE:   [$key] ($valueType): $valuePreview');
      }

      // Check for Cashfree-specific fields
      debugPrint('🔍 RFID-CASHFREE: Checking for Cashfree field variations:');
      debugPrint(
          '🔍 RFID-CASHFREE:   orderId: ${order.containsKey('orderId')} (${order['orderId']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   order_id: ${order.containsKey('order_id')} (${order['order_id']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   orderid: ${order.containsKey('orderid')} (${order['orderid']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   paymentSessionId: ${order.containsKey('paymentSessionId')} (${order['paymentSessionId']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   payment_session_id: ${order.containsKey('payment_session_id')} (${order['payment_session_id']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   sessionId: ${order.containsKey('sessionId')} (${order['sessionId']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   environment: ${order.containsKey('environment')} (${order['environment']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   env: ${order.containsKey('env')} (${order['env']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   txnId: ${order.containsKey('txnId')} (${order['txnId']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   transaction_id: ${order.containsKey('transaction_id')} (${order['transaction_id']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE:   txn_id: ${order.containsKey('txn_id')} (${order['txn_id']?.runtimeType})');
      debugPrint(
          '🔍 RFID-CASHFREE: ========== END DETAILED ANALYSIS ==========');

      // Extract values from order response with null safety
      // The server returns parameters in nested structure (similar to PayU and wallet Cashfree)
      String? environment;
      String? orderId;
      String? paymentSessionId;
      String? txnId;

      // First try to extract from nested cashfreePaymentParams object (expected server format)
      if (order.containsKey('cashfreePaymentParams') &&
          order['cashfreePaymentParams'] is Map) {
        final cashfreeParams =
            order['cashfreePaymentParams'] as Map<String, dynamic>;
        orderId = cashfreeParams['orderId']?.toString() ??
            cashfreeParams['order_id']?.toString();
        paymentSessionId = cashfreeParams['paymentSessionId']?.toString() ??
            cashfreeParams['payment_session_id']?.toString() ??
            cashfreeParams['sessionId']?.toString();
        environment = cashfreeParams['environment']?.toString() ??
            cashfreeParams['env']?.toString();
        txnId = cashfreeParams['txnId']?.toString() ??
            cashfreeParams['transaction_id']?.toString() ??
            cashfreeParams['transactionId']?.toString();
        debugPrint(
            '🔔 RFID: Extracted from cashfreePaymentParams nested object');
      }

      // Second try: extract from nested data object (alternative server format)
      if ((orderId == null ||
              paymentSessionId == null ||
              environment == null ||
              txnId == null) &&
          order.containsKey('data') &&
          order['data'] is Map) {
        final dataParams = order['data'] as Map<String, dynamic>;
        orderId ??= dataParams['orderId']?.toString() ??
            dataParams['order_id']?.toString();
        paymentSessionId ??= dataParams['paymentSessionId']?.toString() ??
            dataParams['payment_session_id']?.toString() ??
            dataParams['sessionId']?.toString();
        environment ??= dataParams['environment']?.toString() ??
            dataParams['env']?.toString();
        txnId ??= dataParams['txnId']?.toString() ??
            dataParams['transaction_id']?.toString() ??
            dataParams['transactionId']?.toString();
        debugPrint('🔔 RFID: Extracted from data nested object');
      }

      // Fallback: try root level with different field name variations
      environment ??= order['environment']?.toString();
      orderId ??= order['orderId']?.toString();
      paymentSessionId ??= order['paymentSessionId']?.toString();
      txnId ??= order['txnId']?.toString();

      if (environment == null ||
          orderId == null ||
          paymentSessionId == null ||
          txnId == null) {
        debugPrint(
            '🔔 RFID: Some parameters still null, trying root level extraction');
        environment ??=
            order['environment']?.toString() ?? order['env']?.toString();
        orderId ??= order['orderId']?.toString() ??
            order['order_id']?.toString() ??
            order['orderid']?.toString();
        paymentSessionId ??= order['paymentSessionId']?.toString() ??
            order['payment_session_id']?.toString() ??
            order['sessionId']?.toString();
        txnId ??= order['txnId']?.toString() ??
            order['transaction_id']?.toString() ??
            order['txn_id']?.toString();
        debugPrint('🔔 RFID: Extracted remaining parameters from root level');
      }

      // Validate required parameters
      if (orderId == null || orderId.isEmpty) {
        throw Exception('Cashfree orderId is missing from server response');
      }
      if (paymentSessionId == null || paymentSessionId.isEmpty) {
        throw Exception(
            'Cashfree paymentSessionId is missing from server response');
      }
      if (environment == null || environment.isEmpty) {
        throw Exception('Cashfree environment is missing from server response');
      }
      if (txnId == null || txnId.isEmpty) {
        throw Exception('Cashfree txnId is missing from server response');
      }

      // Store transaction ID for response handling
      _currentTransactionId = txnId;

      debugPrint('🔔 RFID: Environment: $environment');
      debugPrint('🔔 RFID: OrderId: $orderId');
      debugPrint('🔔 RFID: TxnId: $txnId');

      // Show payment initiation loading
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Initializing Cashfree payment...'),
              ],
            ),
            duration: Duration(seconds: 30),
          ),
        );
      }

      debugPrint('🔔 RFID: ========== CASHFREE SDK INITIALIZATION ==========');
      // Initialize Cashfree SDK
      final inited = await CashfreeService.init(
        environment: environment.toLowerCase() == 'production'
            ? CFEnvironment.PRODUCTION
            : CFEnvironment.SANDBOX,
        enableLogging: true,
      );

      if (!inited) {
        throw Exception('Cashfree SDK initialization failed');
      }

      debugPrint('✅ RFID: Cashfree SDK initialized successfully');

      debugPrint('🔔 RFID: Starting Cashfree transaction...');

      // Update loading message
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Opening Cashfree...'),
              ],
            ),
            duration: Duration(minutes: 5),
          ),
        );
      }

      // Start Cashfree transaction
      final result = await CashfreeService.startWebCheckoutTransaction(
        orderId: orderId,
        paymentSessionId: paymentSessionId,
        environment: environment.toLowerCase() == 'production'
            ? CFEnvironment.PRODUCTION
            : CFEnvironment.SANDBOX,
        timeout: const Duration(minutes: 5),
      );

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      debugPrint(
          '🔔 RFID: Cashfree transaction completed with result: ${result.type}');

      // Handle payment result
      await _handleCashfreeResponse(result, txnId);
    } catch (e) {
      debugPrint('❌ RFID: Cashfree payment error: $e');

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Cashfree payment initialization failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Handle Cashfree payment response
  /// Following the PhonePe and PayU patterns but adapted for Cashfree
  Future<void> _handleCashfreeResponse(
      CashfreePaymentResult result, String txnId) async {
    debugPrint(
        '🔔 RFID: ========== CASHFREE RESPONSE HANDLING START ==========');
    debugPrint('🔔 RFID: Result type: ${result.type}');
    debugPrint('🔔 RFID: Transaction ID: $txnId');

    try {
      final payload = {
        'code': result.data?['status']?.toString() ??
            result.type.toString().split('.').last.toUpperCase(),
        'transactionId': txnId,
        'response': result.data ?? {},
        'call_back_response': result.data ?? {},
      };

      debugPrint('🔔 RFID: Sending Cashfree payment response to server...');

      // Send response to server (similar to PhonePe and PayU but for Cashfree endpoint)
      final response = await _apiService.handleCashfreeResponse(payload);

      debugPrint('🔔 RFID: Server response received: ${response['success']}');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        if (response['success'] == true) {
          // Show success dialog
          _showPaymentSuccessDialog(response);
        } else {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Payment failed'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ RFID: Error handling Cashfree payment response: $e');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Cashfree payment processing failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show payment success dialog
  void _showPaymentSuccessDialog(Map<String, dynamic> response) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A1A),
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 24),
              SizedBox(width: 8),
              Text(
                'Payment Successful!',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Your RFID card order has been placed and payment completed successfully.',
                style: TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              if (_currentTransactionId != null) ...[
                Text(
                  'Transaction ID: $_currentTransactionId',
                  style: const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
              ],
              Text(
                'Amount Paid: ₹${_amountController.text}',
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 8),
              const Text(
                'You will receive your RFID card within 7-10 business days.',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context)
                    .pop(); // Go back to previous screen (matching React: props.navigation.goBack())
              },
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF67C44C)),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build state dropdown widget with proper dark mode support and fixed layout
  Widget _buildStateDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          'State *',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 13,
          ),
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<String>(
          value: _stateController.text.isEmpty ? null : _stateController.text,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            filled: true,
            fillColor: const Color(0xFF1E1E1E),
            hintText: 'Select State',
            hintStyle: const TextStyle(color: Colors.white38, fontSize: 13),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.transparent),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          dropdownColor: const Color(0xFF1E1E1E),
          style: const TextStyle(color: Colors.white, fontSize: 14),
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white70),
          validator: _validateState,
          isExpanded: true,
          items: _indianStates.isNotEmpty
              ? _indianStates.map((String state) {
                  return DropdownMenuItem<String>(
                    value: state,
                    child: Text(
                      state,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList()
              : [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text(
                      'Loading states...',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ),
                ],
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _stateController.text = newValue;
              });
            }
          },
        ),
      ],
    );
  }
}
